import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Shield,
  Clock,
  MapPin,
  Heart,
  Wifi,
  Car,
  Star,
  Zap,
  Smartphone,
  CreditCard,
  MessageCircle,
  BarChart3,
  Lightbulb,
  HeadphonesIcon,
  CheckCircle,
  ArrowRight,
  Sparkles,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";

const getFeatures = (t: any) => [
  {
    icon: Smartphone,
    titleKey: "features.quickBooking.title",
    descriptionKey: "features.quickBooking.description",
    color: "from-blue-500 to-blue-600",
    bgColor: "bg-blue-50",
    category: "booking",
    benefits: [
      "instant-confirmation",
      "mobile-friendly",
      "real-time-availability",
    ],
  },
  {
    icon: CreditCard,
    titleKey: "features.securePayment.title",
    descriptionKey: "features.securePayment.description",
    color: "from-green-500 to-green-600",
    bgColor: "bg-green-50",
    category: "payment",
    benefits: ["ssl-encryption", "multiple-currencies", "fraud-protection"],
  },
  {
    icon: MessageCircle,
    titleKey: "features.whatsappServices.title",
    descriptionKey: "features.whatsappServices.description",
    color: "from-emerald-500 to-emerald-600",
    bgColor: "bg-emerald-50",
    category: "support",
    benefits: ["24-7-availability", "instant-response", "multilingual"],
  },
  {
    icon: HeadphonesIcon,
    titleKey: "features.customerSupport.title",
    descriptionKey: "features.customerSupport.description",
    color: "from-violet-500 to-violet-600",
    bgColor: "bg-violet-50",
    category: "support",
    benefits: ["24-7-support", "expert-team", "quick-resolution"],
  },
  {
    icon: Shield,
    titleKey: "features.flexibleCancellation.title",
    descriptionKey: "features.flexibleCancellation.description",
    color: "from-rose-500 to-rose-600",
    bgColor: "bg-rose-50",
    category: "policy",
    benefits: ["free-cancellation", "full-refund", "easy-modification"],
  },
  {
    icon: Sparkles,
    titleKey: "features.premiumAmenities.title",
    descriptionKey: "features.premiumAmenities.description",
    color: "from-amber-500 to-amber-600",
    bgColor: "bg-amber-50",
    category: "amenities",
    benefits: ["luxury-furnishing", "high-speed-wifi", "premium-linens"],
  },
  {
    icon: BarChart3,
    titleKey: "features.integratedManagement.title",
    descriptionKey: "features.integratedManagement.description",
    color: "from-purple-500 to-purple-600",
    bgColor: "bg-purple-50",
    category: "management",
    benefits: ["smart-dashboard", "real-time-updates", "automated-processes"],
  },
  {
    icon: MapPin,
    titleKey: "features.primeLocations.title",
    descriptionKey: "features.primeLocations.description",
    color: "from-cyan-500 to-cyan-600",
    bgColor: "bg-cyan-50",
    category: "location",
    benefits: ["city-center", "transport-access", "local-attractions"],
  },
];

const stats = [
  { value: 10000, label: "Happy Guests", suffix: "+" },
  { value: 500, label: "Premium Properties", suffix: "+" },
  { value: 99, label: "Satisfaction Rate", suffix: "%" },
  { value: 24, label: "Hour Support", suffix: "/7" },
];

const FeaturesSection = () => {
  const { t } = useTranslation();
  const features = getFeatures(t);
  const [activeTab, setActiveTab] = useState("all");
  const [animatedStats, setAnimatedStats] = useState(stats.map(() => 0));
  const [hasAnimated, setHasAnimated] = useState(false);

  // Animate numbers on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !hasAnimated) {
          setHasAnimated(true);
          stats.forEach((stat, index) => {
            let current = 0;
            const increment = stat.value / 100;
            const timer = setInterval(() => {
              current += increment;
              if (current >= stat.value) {
                current = stat.value;
                clearInterval(timer);
              }
              setAnimatedStats((prev) => {
                const newStats = [...prev];
                newStats[index] = Math.floor(current);
                return newStats;
              });
            }, 20);
          });
        }
      },
      { threshold: 0.5 }
    );

    const element = document.getElementById("stats-section");
    if (element) observer.observe(element);

    return () => observer.disconnect();
  }, [hasAnimated]);

  const filteredFeatures =
    activeTab === "all"
      ? features
      : features.filter((feature) => feature.category === activeTab);

  const categories = [
    { id: "all", label: "All Features", icon: Star },
    { id: "booking", label: "Booking", icon: Smartphone },
    { id: "support", label: "Support", icon: HeadphonesIcon },
    { id: "amenities", label: "Amenities", icon: Sparkles },
  ];

  return (
    <section className="py-24 bg-gradient-to-b from-background via-background/50 to-background relative overflow-hidden">
      {/* Enhanced Background decorations */}
      <div className="absolute top-10 left-10 opacity-5">
        <Star className="w-32 h-32 text-primary animate-pulse" />
      </div>
      <div className="absolute bottom-10 right-10 opacity-5">
        <Zap className="w-24 h-24 text-primary animate-bounce" />
      </div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-3">
        <div className="w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-primary/20 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${3 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Enhanced Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-primary/10 via-primary/20 to-primary/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6 border border-primary/20">
            <Star className="w-4 h-4 text-primary mr-2 animate-spin-slow" />
            <span className="text-primary text-sm font-semibold tracking-wide">
              {t("features.badge", "Why Choose Hala")}
            </span>
            <Sparkles className="w-4 h-4 text-primary ml-2" />
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-6xl font-bold text-foreground mb-6 leading-tight bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text">
            {t("features.title")}
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            {t("features.description")}
          </p>
        </div>

        {/* Stats Section */}
        <div
          id="stats-section"
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        >
          {stats.map((stat, index) => (
            <div
              key={index}
              className="text-center p-6 rounded-2xl bg-gradient-to-br from-primary/5 to-transparent border border-primary/10 backdrop-blur-sm"
            >
              <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                {animatedStats[index]}
                {stat.suffix}
              </div>
              <div className="text-sm text-muted-foreground font-medium">
                {t(
                  `stats.${stat.label.toLowerCase().replace(" ", "")}`,
                  stat.label
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Category Tabs */}
        <div
          className="flex flex-wrap justify-center gap-2 mb-12"
          role="tablist"
          aria-label="Feature categories"
        >
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={activeTab === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setActiveTab(category.id)}
              className={`transition-all duration-300 ${
                activeTab === category.id
                  ? "bg-primary text-primary-foreground shadow-lg scale-105"
                  : "hover:bg-primary/10 hover:scale-105"
              }`}
              role="tab"
              aria-selected={activeTab === category.id}
              aria-controls={`features-panel-${category.id}`}
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  setActiveTab(category.id);
                }
              }}
            >
              <category.icon className="w-4 h-4 mr-2" aria-hidden="true" />
              {category.label}
            </Button>
          ))}
        </div>

        {/* Enhanced Features Grid */}
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          role="tabpanel"
          id={`features-panel-${activeTab}`}
          aria-labelledby={`tab-${activeTab}`}
        >
          {filteredFeatures.map((feature, index) => (
            <Card
              key={`${feature.titleKey}-${index}`}
              className="group border-0 shadow-lg hover:shadow-2xl transition-all duration-700 bg-gradient-to-br from-background via-background/80 to-primary/5 hover:-translate-y-3 hover:scale-105 cursor-pointer backdrop-blur-sm border border-primary/10 hover:border-primary/30 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
              style={{
                animationDelay: `${index * 150}ms`,
                transform: `perspective(1000px) rotateY(${
                  index % 2 === 0 ? "-2deg" : "2deg"
                })`,
              }}
              role="article"
              aria-labelledby={`feature-title-${index}`}
              tabIndex={0}
            >
              <CardContent className="p-6 text-center relative overflow-hidden h-full flex flex-col">
                {/* Enhanced Background pattern */}
                <div className="absolute top-0 right-0 opacity-10">
                  <div className="w-24 h-24 bg-gradient-to-br from-primary via-primary/70 to-primary/30 rounded-full transform translate-x-8 -translate-y-8 group-hover:scale-150 transition-transform duration-700"></div>
                </div>
                <div className="absolute bottom-0 left-0 opacity-5">
                  <div className="w-16 h-16 bg-gradient-to-tr from-secondary to-secondary/50 rounded-full transform -translate-x-4 translate-y-4 group-hover:scale-125 transition-transform duration-500"></div>
                </div>

                {/* Enhanced Icon with multiple effects */}
                <div className="relative mb-6">
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
                  <div
                    className={`relative w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center mx-auto group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 shadow-xl group-hover:shadow-2xl`}
                    role="img"
                    aria-label={t(feature.titleKey)}
                  >
                    <feature.icon
                      className="h-8 w-8 text-white group-hover:scale-110 transition-transform duration-300"
                      aria-hidden="true"
                    />
                    <div className="absolute inset-0 bg-white/20 rounded-2xl group-hover:bg-white/30 transition-colors duration-300"></div>
                  </div>
                </div>

                {/* Enhanced Typography */}
                <h3
                  id={`feature-title-${index}`}
                  className="text-lg font-bold text-foreground mb-3 group-hover:text-primary transition-colors duration-300 group-hover:scale-105 transform"
                >
                  {t(feature.titleKey)}
                </h3>
                <p className="text-muted-foreground leading-relaxed text-sm mb-4 flex-grow">
                  {t(feature.descriptionKey)}
                </p>

                {/* Benefits Pills */}
                <div className="flex flex-wrap gap-1 justify-center mb-4">
                  {feature.benefits?.slice(0, 2).map((benefit, i) => (
                    <span
                      key={benefit}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary border border-primary/20"
                    >
                      <CheckCircle className="w-3 h-3 mr-1" />
                      {t(
                        `features.benefits.${benefit}`,
                        benefit.replace("-", " ")
                      )}
                    </span>
                  ))}
                </div>

                {/* Learn More Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="group/btn w-full mt-auto opacity-0 group-hover:opacity-100 focus:opacity-100 transition-all duration-300 hover:bg-primary/10 focus:bg-primary/10"
                  aria-label={`Learn more about ${t(feature.titleKey)}`}
                >
                  <span className="text-primary font-medium">
                    {t("common.learnMore", "Learn More")}
                  </span>
                  <ArrowRight
                    className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform duration-200"
                    aria-hidden="true"
                  />
                </Button>

                {/* Enhanced Decorative elements */}
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-primary/40 to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>
                <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-transparent via-secondary/40 to-transparent transform scale-y-0 group-hover:scale-y-100 transition-transform duration-700"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Enhanced Bottom CTA */}
        <div className="text-center mt-20">
          <div className="relative inline-block">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-secondary/20 to-primary/20 rounded-2xl blur-xl"></div>
            <div className="relative bg-gradient-to-r from-background/80 via-background to-background/80 backdrop-blur-sm rounded-2xl border border-primary/20 p-8">
              <div className="inline-flex items-center space-x-3 text-muted-foreground mb-6">
                <Star className="w-6 h-6 fill-yellow-400 text-yellow-400 animate-pulse" />
                <span className="text-lg font-medium bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent">
                  {t(
                    "features.excellence",
                    "Experience excellence in every detail"
                  )}
                </span>
                <Star className="w-6 h-6 fill-yellow-400 text-yellow-400 animate-pulse" />
              </div>

              {/* Call to Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group">
                  <span className="mr-2">Explore Properties</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
                </Button>
                <Button
                  variant="outline"
                  className="border-primary/30 hover:bg-primary/5 px-8 py-3 rounded-xl"
                >
                  Learn More
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
