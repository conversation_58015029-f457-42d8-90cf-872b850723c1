import { useState } from "react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Calendar as CalendarIcon,
  Users,
  Search,
  Plus,
  Minus,
} from "lucide-react";
import { cn } from "@/lib/utils";

const SearchForm = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [activeField, setActiveField] = useState<string | null>(null);
  const [checkIn, setCheckIn] = useState<Date>();
  const [checkOut, setCheckOut] = useState<Date>();
  const [guests, setGuests] = useState(1);

  return (
    <Card className="w-full max-w-5xl mx-auto bg-white/95 backdrop-blur-xl shadow-2xl border-0 rounded-2xl overflow-hidden">
      <div className="p-6 lg:p-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-0">
          {/* Check-in Date */}
          <div className="relative group lg:border-r border-gray-200 last:border-r-0 p-6 hover:bg-blue-50/50 transition-all duration-200 rounded-xl lg:rounded-none">
            <div className="text-xs font-bold text-gray-600 uppercase tracking-wide mb-3">
              {t("search.checkIn")}
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full h-auto p-0 justify-start text-left font-normal hover:bg-transparent group-hover:scale-105 transition-transform",
                    !checkIn && "text-gray-500"
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`p-2 rounded-lg bg-blue-100 group-hover:bg-blue-200 transition-colors ${
                        isRTL ? "ml-3" : ""
                      }`}
                    >
                      <CalendarIcon className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="text-base font-semibold text-gray-900">
                        {checkIn
                          ? format(checkIn, "MMM dd")
                          : t("search.selectDate")}
                      </div>
                      {checkIn && (
                        <div className="text-sm text-gray-500">
                          {format(checkIn, "yyyy")}
                        </div>
                      )}
                    </div>
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0 bg-white border border-gray-200 shadow-xl rounded-xl z-50"
                align="start"
              >
                <Calendar
                  mode="single"
                  selected={checkIn}
                  onSelect={setCheckIn}
                  disabled={(date) => date < new Date()}
                  initialFocus
                  className="pointer-events-auto rounded-xl"
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Check-out Date */}
          <div className="relative group lg:border-r border-gray-200 last:border-r-0 p-6 hover:bg-blue-50/50 transition-all duration-200 rounded-xl lg:rounded-none">
            <div className="text-xs font-bold text-gray-600 uppercase tracking-wide mb-3">
              {t("search.checkOut")}
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full h-auto p-0 justify-start text-left font-normal hover:bg-transparent group-hover:scale-105 transition-transform",
                    !checkOut && "text-gray-500"
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`p-2 rounded-lg bg-blue-100 group-hover:bg-blue-200 transition-colors ${
                        isRTL ? "ml-3" : ""
                      }`}
                    >
                      <CalendarIcon className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="text-base font-semibold text-gray-900">
                        {checkOut
                          ? format(checkOut, "MMM dd")
                          : t("search.selectDate")}
                      </div>
                      {checkOut && (
                        <div className="text-sm text-gray-500">
                          {format(checkOut, "yyyy")}
                        </div>
                      )}
                    </div>
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0 bg-white border border-gray-200 shadow-xl rounded-xl z-50"
                align="start"
              >
                <Calendar
                  mode="single"
                  selected={checkOut}
                  onSelect={setCheckOut}
                  disabled={(date) =>
                    date < new Date() || (checkIn && date <= checkIn)
                  }
                  initialFocus
                  className="pointer-events-auto rounded-xl"
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Guests */}
          <div className="relative group lg:border-r border-gray-200 last:border-r-0 p-6 hover:bg-blue-50/50 transition-all duration-200 rounded-xl lg:rounded-none">
            <div className="text-xs font-bold text-gray-600 uppercase tracking-wide mb-3">
              {t("search.guests")}
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full h-auto p-0 justify-start hover:bg-transparent group-hover:scale-105 transition-transform"
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`p-2 rounded-lg bg-blue-100 group-hover:bg-blue-200 transition-colors ${
                        isRTL ? "ml-3" : ""
                      }`}
                    >
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="text-base font-semibold text-gray-900">
                        {guests}{" "}
                        {guests === 1
                          ? t("search.guest")
                          : t("search.guestsPlural")}
                      </div>
                      <div className="text-sm text-gray-500">
                        اختر عدد النزلاء
                      </div>
                    </div>
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-72 p-6 bg-white border border-gray-200 shadow-xl rounded-xl z-50"
                align="start"
              >
                <div className="flex items-center justify-between">
                  <span className="text-base font-semibold text-gray-900">
                    {t("search.guests")}
                  </span>
                  <div className="flex items-center space-x-4">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-10 w-10 rounded-full border-gray-300 hover:bg-blue-50 hover:border-blue-300"
                      onClick={() => setGuests(Math.max(1, guests - 1))}
                      disabled={guests <= 1}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <span className="w-12 text-center text-lg font-semibold text-gray-900">
                      {guests}
                    </span>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-10 w-10 rounded-full border-gray-300 hover:bg-blue-50 hover:border-blue-300"
                      onClick={() => setGuests(Math.min(10, guests + 1))}
                      disabled={guests >= 10}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Search Button */}
          <div className="relative p-6 flex items-center">
            <Button
              className="w-full h-14 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 font-bold text-lg rounded-2xl border-0"
              size="lg"
            >
              <Search className={`h-6 w-6 ${isRTL ? "ml-3" : "mr-3"}`} />
              {t("search.search")}
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default SearchForm;
